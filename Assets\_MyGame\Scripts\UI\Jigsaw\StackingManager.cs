using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 叠加管理器，负责处理同一格子多个拼块的布局和层级
/// </summary>
public class StackingManager
{
    private const float STACK_OFFSET_X = 2f; // 轻微偏移避免完全重叠
    private const float STACK_OFFSET_Y = 2f;
    private JigsawPanel parentPanel;
    
    public StackingManager(JigsawPanel panel)
    {
        parentPanel = panel;
    }
    
    /// <summary>
    /// 应用叠加布局
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <param name="pieces">拼块列表</param>
    public void ApplyStackingLayout(Vector2Int gridPos, List<JigsawPiece> pieces)
    {
        if (pieces == null || pieces.Count == 0) return;
        
        Vector2 basePosition = GetGridCenterPosition(gridPos);
        
        for (int i = 0; i < pieces.Count; i++)
        {
            var piece = pieces[i];
            if (piece == null) continue;
            
            // 基础位置
            Vector2 targetPos = basePosition;
            
            if (i > 0) // 叠加的拼块需要轻微偏移
            {
                float offsetX = (i % 3 - 1) * STACK_OFFSET_X; // -1, 0, 1 的偏移模式
                float offsetY = (i / 3) * STACK_OFFSET_Y;     // 每3个换一行
                targetPos += new Vector2(offsetX, offsetY);
            }
            
            // 应用位置（考虑pivot）
            Vector2 piecePos = targetPos - new Vector2(piece.width * 0.5f, piece.height * 0.5f);
            
            // 如果拼块在拖拽层中，需要转换坐标
            if (piece.parent != null)
            {
                Vector2 globalPos = parentPanel.OperationLayerLocalToGlobal(targetPos);
                Vector2 localPos = piece.parent.GlobalToLocal(globalPos);
                localPos -= new Vector2(piece.width * 0.5f, piece.height * 0.5f);
                
                // 使用缓动动画移动到目标位置
                var tween = piece.TweenMove(localPos, 0.3f);
                
                // 在动画过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    UpdateThicknessPosition(piece);
                });
            }
            else
            {
                // 使用缓动动画移动到目标位置
                var tween = piece.TweenMove(piecePos, 0.3f);
                
                // 在动画过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    UpdateThicknessPosition(piece);
                });
            }
            
            // 同步thickness位置
            UpdateThicknessPosition(piece);
        }
    }
    
    /// <summary>
    /// 处理重叠冲突
    /// </summary>
    /// <param name="newPiece">新拼块</param>
    /// <param name="existingPieces">已存在的拼块</param>
    public void HandleOverlapConflict(JigsawPiece newPiece, List<JigsawPiece> existingPieces)
    {
        if (existingPieces == null || existingPieces.Count == 0) return;
        
        // 检查是否有正确位置的拼块
        var correctPieces = existingPieces.Where(p => IsInCorrectPosition(p)).ToList();
        var incorrectPieces = existingPieces.Where(p => !IsInCorrectPosition(p)).ToList();
        
        if (IsInCorrectPosition(newPiece))
        {
            // 新拼块在正确位置，应该在底层
            ApplyCorrectPositionPriority(newPiece, correctPieces, incorrectPieces);
        }
        else
        {
            // 新拼块不在正确位置，放在叠加层
            ApplyOverlayStacking(newPiece, existingPieces);
        }
    }
    
    /// <summary>
    /// 应用正确位置优先级
    /// </summary>
    /// <param name="newPiece">新拼块</param>
    /// <param name="correctPieces">正确位置的拼块</param>
    /// <param name="incorrectPieces">错误位置的拼块</param>
    private void ApplyCorrectPositionPriority(JigsawPiece newPiece, List<JigsawPiece> correctPieces, List<JigsawPiece> incorrectPieces)
    {
        // 正确位置的拼块排在底层，错误位置的在上层
        var allPieces = new List<JigsawPiece>();
        allPieces.AddRange(correctPieces);
        allPieces.Insert(0, newPiece); // 新拼块也在正确位置，插入到正确位置拼块的最前面
        allPieces.AddRange(incorrectPieces);
        
        Vector2Int gridPos = CalculateGridPosition(newPiece);
        ApplyStackingLayout(gridPos, allPieces);
    }
    
    /// <summary>
    /// 应用叠加堆叠
    /// </summary>
    /// <param name="newPiece">新拼块</param>
    /// <param name="existingPieces">已存在的拼块</param>
    private void ApplyOverlayStacking(JigsawPiece newPiece, List<JigsawPiece> existingPieces)
    {
        // 新拼块不在正确位置，添加到叠加层
        var allPieces = new List<JigsawPiece>(existingPieces);
        allPieces.Add(newPiece);
        
        Vector2Int gridPos = CalculateGridPosition(newPiece);
        ApplyStackingLayout(gridPos, allPieces);
    }
    
    /// <summary>
    /// 获取网格中心位置
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <returns>世界坐标中的位置</returns>
    private Vector2 GetGridCenterPosition(Vector2Int gridPos)
    {
        if (parentPanel == null) return Vector2.zero;
        
        return parentPanel.GetLocalPosition(gridPos);
    }
    
    /// <summary>
    /// 检查拼块是否在正确位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>是否在正确位置</returns>
    private bool IsInCorrectPosition(JigsawPiece piece)
    {
        if (piece == null || parentPanel == null) return false;
        
        Vector2Int originalPos = JigsawGroup.GetGridPosition(piece.pieceIndex);
        Vector2Int currentPos = CalculateGridPosition(piece);
        
        return originalPos == currentPos;
    }
    
    /// <summary>
    /// 计算拼块的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int CalculateGridPosition(JigsawPiece piece)
    {
        if (piece == null || parentPanel == null) return Vector2Int.zero;
        
        Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
        Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);
        
        return parentPanel.GetGridPosition(operationLayerLocalPos);
    }
    
    /// <summary>
    /// 更新thickness位置
    /// </summary>
    /// <param name="piece">拼块</param>
    private void UpdateThicknessPosition(JigsawPiece piece)
    {
        if (piece == null || parentPanel == null) return;
        
        // 使用新的层级管理系统更新thickness位置
        var operationLayer = parentPanel.GetOperationLayer();
        operationLayer?.OnPiecePositionChanged(piece);
    }
    
    /// <summary>
    /// 重新排列网格位置的所有拼块
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <param name="pieces">拼块列表</param>
    public void RearrangePiecesAtGrid(Vector2Int gridPos, List<JigsawPiece> pieces)
    {
        if (pieces == null || pieces.Count == 0) return;
        
        // 按优先级排序：正确位置 > 错误位置
        var sortedPieces = pieces.OrderBy(p => IsInCorrectPosition(p) ? 0 : 1).ToList();
        
        ApplyStackingLayout(gridPos, sortedPieces);
    }
    
    /// <summary>
    /// 计算拼块的堆叠优先级
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>优先级值（越小优先级越高）</returns>
    private int GetStackingPriority(JigsawPiece piece)
    {
        if (IsInCorrectPosition(piece))
            return 0; // 正确位置最高优先级
        else
            return 1; // 错误位置较低优先级
    }
    
    /// <summary>
    /// 检查是否需要重新排列
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <param name="pieces">拼块列表</param>
    /// <returns>是否需要重新排列</returns>
    public bool NeedsRearrangement(Vector2Int gridPos, List<JigsawPiece> pieces)
    {
        if (pieces == null || pieces.Count <= 1) return false;
        
        // 检查是否有正确位置的拼块在错误位置的拼块上方
        for (int i = 0; i < pieces.Count - 1; i++)
        {
            var lowerPiece = pieces[i];
            var upperPiece = pieces[i + 1];
            
            bool lowerIsCorrect = IsInCorrectPosition(lowerPiece);
            bool upperIsCorrect = IsInCorrectPosition(upperPiece);
            
            // 如果下层拼块在错误位置，上层拼块在正确位置，则需要重排
            if (!lowerIsCorrect && upperIsCorrect)
                return true;
        }
        
        return false;
    }
}