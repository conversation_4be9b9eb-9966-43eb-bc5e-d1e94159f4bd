using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 网格位置管理器，负责管理每个网格位置的拼块堆叠情况
/// </summary>
public class GridPositionManager
{
    // 记录每个网格位置的拼块堆叠情况
    private Dictionary<Vector2Int, List<JigsawPiece>> gridPieces = 
        new Dictionary<Vector2Int, List<JigsawPiece>>();
    
    private SmartLayerManager layerManager;
    
    public GridPositionManager(SmartLayerManager manager)
    {
        layerManager = manager;
    }
    
    /// <summary>
    /// 添加拼块到网格位置
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    public void AddPieceToGrid(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            gridPieces[gridPos] = new List<JigsawPiece>();
            
        if (!gridPieces[gridPos].Contains(piece))
        {
            gridPieces[gridPos].Add(piece);
            UpdateGridLayering(gridPos);
        }
    }
    
    /// <summary>
    /// 从网格位置移除拼块
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    public void RemovePieceFromGrid(Vector2Int gridPos, JigsawPiece piece)
    {
        if (gridPieces.ContainsKey(gridPos))
        {
            gridPieces[gridPos].Remove(piece);
            if (gridPieces[gridPos].Count == 0)
            {
                gridPieces.Remove(gridPos);
            }
            else
            {
                UpdateGridLayering(gridPos);
            }
        }
    }
    
    /// <summary>
    /// 获取指定网格位置的所有拼块
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetPiecesAtGrid(Vector2Int gridPos)
    {
        if (gridPieces.ContainsKey(gridPos))
            return new List<JigsawPiece>(gridPieces[gridPos]);
        return new List<JigsawPiece>();
    }
    
    /// <summary>
    /// 检查拼块是否处于叠加位置
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    /// <returns>是否为叠加位置</returns>
    public bool IsOverlayPosition(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            return false;
            
        var pieces = gridPieces[gridPos];
        int index = pieces.IndexOf(piece);
        return index > 0; // 第一个不是overlay，后续都是overlay
    }
    
    /// <summary>
    /// 获取拼块在网格中的堆叠索引
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="piece">拼块</param>
    /// <returns>堆叠索引</returns>
    public int GetStackIndex(Vector2Int gridPos, JigsawPiece piece)
    {
        if (!gridPieces.ContainsKey(gridPos))
            return 0;
            
        var pieces = gridPieces[gridPos];
        int index = pieces.IndexOf(piece);
        return index >= 0 ? index : 0;
    }
    
    /// <summary>
    /// 检查网格位置是否有其他拼块
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <param name="excludePiece">要排除的拼块</param>
    /// <returns>是否有其他拼块</returns>
    public bool HasOtherPieces(Vector2Int gridPos, JigsawPiece excludePiece = null)
    {
        if (!gridPieces.ContainsKey(gridPos))
            return false;
            
        var pieces = gridPieces[gridPos];
        if (excludePiece == null)
            return pieces.Count > 0;
            
        return pieces.Any(p => p != excludePiece);
    }
    
    /// <summary>
    /// 更新网格位置的层级排列
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    private void UpdateGridLayering(Vector2Int gridPos)
    {
        if (!gridPieces.ContainsKey(gridPos)) return;
        
        var pieces = gridPieces[gridPos];
        
        // 按正确位置优先级排序：正确位置的拼块排在前面（底层）
        pieces.Sort((p1, p2) => 
        {
            bool p1Correct = IsInCorrectPosition(p1, gridPos);
            bool p2Correct = IsInCorrectPosition(p2, gridPos);
            
            if (p1Correct && !p2Correct) return -1;
            if (!p1Correct && p2Correct) return 1;
            return 0; // 保持相对顺序
        });
        
        // 更新每个拼块的层级
        for (int i = 0; i < pieces.Count; i++)
        {
            var piece = pieces[i];
            layerManager?.UpdatePieceLayer(piece);
        }
    }
    
    /// <summary>
    /// 检查拼块是否在正确位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="gridPos">网格坐标</param>
    /// <returns>是否在正确位置</returns>
    private bool IsInCorrectPosition(JigsawPiece piece, Vector2Int gridPos)
    {
        Vector2Int originalPos = JigsawGroup.GetGridPosition(piece.pieceIndex);
        return originalPos == gridPos;
    }
    
    /// <summary>
    /// 获取所有网格位置
    /// </summary>
    /// <returns>网格位置列表</returns>
    public List<Vector2Int> GetAllGridPositions()
    {
        return new List<Vector2Int>(gridPieces.Keys);
    }
    
    /// <summary>
    /// 清空所有网格位置数据
    /// </summary>
    public void Clear()
    {
        gridPieces.Clear();
    }
    
    /// <summary>
    /// 获取网格位置的拼块数量
    /// </summary>
    /// <param name="gridPos">网格坐标</param>
    /// <returns>拼块数量</returns>
    public int GetPieceCount(Vector2Int gridPos)
    {
        if (gridPieces.ContainsKey(gridPos))
            return gridPieces[gridPos].Count;
        return 0;
    }
}